﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

// 读取数据文件
string[] lines = File.ReadAllLines("../磁控点总数据1.txt");

// 统计所有2数组合的频次
var combinations = new Dictionary<string, int>();
int validLines = 0;

foreach (string line in lines)
{
    string trimmedLine = line.Trim();
    if (string.IsNullOrEmpty(trimmedLine)) continue;

    validLines++;
    string[] parts = trimmedLine.Split('-');
    List<int> numbers = parts.Select(int.Parse).ToList();

    Console.WriteLine($"第{validLines}行: {numbers.Count}个数字");

    // 生成所有2数组合
    for (int i = 0; i < numbers.Count - 1; i++)
    {
        for (int j = i + 1; j < numbers.Count; j++)
        {
            string combination = $"{numbers[i]}-{numbers[j]}";
            if (combinations.ContainsKey(combination))
            {
                combinations[combination]++;
            }
            else
            {
                combinations[combination] = 1;
            }
        }
    }
}

Console.WriteLine($"\n总共{validLines}行有效数据");
Console.WriteLine($"生成了{combinations.Count}种不同的2数组合");

// 分析频次分布
var frequencyGroups = combinations.GroupBy(kvp => kvp.Value)
                                 .OrderBy(g => g.Key)
                                 .ToList();

Console.WriteLine("\n频次分布:");
foreach (var group in frequencyGroups)
{
    Console.WriteLine($"出现{group.Key}次的2数组合: {group.Count()}个");
}

// 检查5-50范围内的组合
var validCombinations = combinations.Where(kvp => kvp.Value >= 5 && kvp.Value <= 50).ToList();
Console.WriteLine($"\n频次在5-50范围内的2数组合: {validCombinations.Count}个");

// 检查被剔除的组合
var allPossible = new HashSet<string>();
for (int i = 1; i <= 32; i++)
{
    for (int j = i + 1; j <= 33; j++)
    {
        allPossible.Add($"{i}-{j}");
    }
}

var invalidCombinations = allPossible.Except(validCombinations.Select(kvp => kvp.Key)).ToList();
Console.WriteLine($"被剔除的2数组合: {invalidCombinations.Count}个");

Console.WriteLine("\n被剔除的2数组合列表:");
foreach (var invalid in invalidCombinations.OrderBy(x => x))
{
    if (combinations.ContainsKey(invalid))
    {
        Console.WriteLine($"{invalid} (出现{combinations[invalid]}次)");
    }
    else
    {
        Console.WriteLine($"{invalid} (未出现)");
    }
}
